{"name": "@libra/docs", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "bun next dev --turbo -p 3003", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "update": "bun update"}, "dependencies": {"fumadocs-core": "^15.6.7", "fumadocs-mdx": "^11.7.3", "fumadocs-ui": "^15.6.7", "motion": "^12.23.12", "octokit": "^5.0.3", "@orama/tokenizers": "^3.1.11", "@orama/orama": "^3.1.11", "zod": "^4.0.14"}, "devDependencies": {"@types/mdx": "^2.0.13"}}